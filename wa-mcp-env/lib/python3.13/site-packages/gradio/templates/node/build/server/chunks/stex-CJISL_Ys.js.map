{"version": 3, "file": "stex-CJISL_Ys.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/stex.js"], "sourcesContent": ["function mkStex(mathMode) {\n  function pushCommand(state, command) {\n    state.cmdState.push(command);\n  }\n  function peekCommand(state) {\n    if (state.cmdState.length > 0) {\n      return state.cmdState[state.cmdState.length - 1];\n    } else {\n      return null;\n    }\n  }\n  function popCommand(state) {\n    var plug = state.cmdState.pop();\n    if (plug) {\n      plug.closeBracket();\n    }\n  }\n  function getMostPowerful(state) {\n    var context = state.cmdState;\n    for (var i = context.length - 1; i >= 0; i--) {\n      var plug = context[i];\n      if (plug.name == \"DEFAULT\") {\n        continue;\n      }\n      return plug;\n    }\n    return { styleIdentifier: function() {\n      return null;\n    } };\n  }\n  function addPluginPattern(pluginName, cmdStyle, styles) {\n    return function() {\n      this.name = pluginName;\n      this.bracketNo = 0;\n      this.style = cmdStyle;\n      this.styles = styles;\n      this.argument = null;\n      this.styleIdentifier = function() {\n        return this.styles[this.bracketNo - 1] || null;\n      };\n      this.openBracket = function() {\n        this.bracketNo++;\n        return \"bracket\";\n      };\n      this.closeBracket = function() {\n      };\n    };\n  }\n  var plugins = {};\n  plugins[\"importmodule\"] = addPluginPattern(\"importmodule\", \"tag\", [\"string\", \"builtin\"]);\n  plugins[\"documentclass\"] = addPluginPattern(\"documentclass\", \"tag\", [\"\", \"atom\"]);\n  plugins[\"usepackage\"] = addPluginPattern(\"usepackage\", \"tag\", [\"atom\"]);\n  plugins[\"begin\"] = addPluginPattern(\"begin\", \"tag\", [\"atom\"]);\n  plugins[\"end\"] = addPluginPattern(\"end\", \"tag\", [\"atom\"]);\n  plugins[\"label\"] = addPluginPattern(\"label\", \"tag\", [\"atom\"]);\n  plugins[\"ref\"] = addPluginPattern(\"ref\", \"tag\", [\"atom\"]);\n  plugins[\"eqref\"] = addPluginPattern(\"eqref\", \"tag\", [\"atom\"]);\n  plugins[\"cite\"] = addPluginPattern(\"cite\", \"tag\", [\"atom\"]);\n  plugins[\"bibitem\"] = addPluginPattern(\"bibitem\", \"tag\", [\"atom\"]);\n  plugins[\"Bibitem\"] = addPluginPattern(\"Bibitem\", \"tag\", [\"atom\"]);\n  plugins[\"RBibitem\"] = addPluginPattern(\"RBibitem\", \"tag\", [\"atom\"]);\n  plugins[\"DEFAULT\"] = function() {\n    this.name = \"DEFAULT\";\n    this.style = \"tag\";\n    this.styleIdentifier = this.openBracket = this.closeBracket = function() {\n    };\n  };\n  function setState(state, f) {\n    state.f = f;\n  }\n  function normal(source, state) {\n    var plug;\n    if (source.match(/^\\\\[a-zA-Z@\\xc0-\\u1fff\\u2060-\\uffff]+/)) {\n      var cmdName = source.current().slice(1);\n      plug = plugins.hasOwnProperty(cmdName) ? plugins[cmdName] : plugins[\"DEFAULT\"];\n      plug = new plug();\n      pushCommand(state, plug);\n      setState(state, beginParams);\n      return plug.style;\n    }\n    if (source.match(/^\\\\[$&%#{}_]/)) {\n      return \"tag\";\n    }\n    if (source.match(/^\\\\[,;!\\/\\\\]/)) {\n      return \"tag\";\n    }\n    if (source.match(\"\\\\[\")) {\n      setState(state, function(source2, state2) {\n        return inMathMode(source2, state2, \"\\\\]\");\n      });\n      return \"keyword\";\n    }\n    if (source.match(\"\\\\(\")) {\n      setState(state, function(source2, state2) {\n        return inMathMode(source2, state2, \"\\\\)\");\n      });\n      return \"keyword\";\n    }\n    if (source.match(\"$$\")) {\n      setState(state, function(source2, state2) {\n        return inMathMode(source2, state2, \"$$\");\n      });\n      return \"keyword\";\n    }\n    if (source.match(\"$\")) {\n      setState(state, function(source2, state2) {\n        return inMathMode(source2, state2, \"$\");\n      });\n      return \"keyword\";\n    }\n    var ch = source.next();\n    if (ch == \"%\") {\n      source.skipToEnd();\n      return \"comment\";\n    } else if (ch == \"}\" || ch == \"]\") {\n      plug = peekCommand(state);\n      if (plug) {\n        plug.closeBracket(ch);\n        setState(state, beginParams);\n      } else {\n        return \"error\";\n      }\n      return \"bracket\";\n    } else if (ch == \"{\" || ch == \"[\") {\n      plug = plugins[\"DEFAULT\"];\n      plug = new plug();\n      pushCommand(state, plug);\n      return \"bracket\";\n    } else if (/\\d/.test(ch)) {\n      source.eatWhile(/[\\w.%]/);\n      return \"atom\";\n    } else {\n      source.eatWhile(/[\\w\\-_]/);\n      plug = getMostPowerful(state);\n      if (plug.name == \"begin\") {\n        plug.argument = source.current();\n      }\n      return plug.styleIdentifier();\n    }\n  }\n  function inMathMode(source, state, endModeSeq) {\n    if (source.eatSpace()) {\n      return null;\n    }\n    if (endModeSeq && source.match(endModeSeq)) {\n      setState(state, normal);\n      return \"keyword\";\n    }\n    if (source.match(/^\\\\[a-zA-Z@]+/)) {\n      return \"tag\";\n    }\n    if (source.match(/^[a-zA-Z]+/)) {\n      return \"variableName.special\";\n    }\n    if (source.match(/^\\\\[$&%#{}_]/)) {\n      return \"tag\";\n    }\n    if (source.match(/^\\\\[,;!\\/]/)) {\n      return \"tag\";\n    }\n    if (source.match(/^[\\^_&]/)) {\n      return \"tag\";\n    }\n    if (source.match(/^[+\\-<>|=,\\/@!*:;'\"`~#?]/)) {\n      return null;\n    }\n    if (source.match(/^(\\d+\\.\\d*|\\d*\\.\\d+|\\d+)/)) {\n      return \"number\";\n    }\n    var ch = source.next();\n    if (ch == \"{\" || ch == \"}\" || ch == \"[\" || ch == \"]\" || ch == \"(\" || ch == \")\") {\n      return \"bracket\";\n    }\n    if (ch == \"%\") {\n      source.skipToEnd();\n      return \"comment\";\n    }\n    return \"error\";\n  }\n  function beginParams(source, state) {\n    var ch = source.peek(), lastPlug;\n    if (ch == \"{\" || ch == \"[\") {\n      lastPlug = peekCommand(state);\n      lastPlug.openBracket(ch);\n      source.eat(ch);\n      setState(state, normal);\n      return \"bracket\";\n    }\n    if (/[ \\t\\r]/.test(ch)) {\n      source.eat(ch);\n      return null;\n    }\n    setState(state, normal);\n    popCommand(state);\n    return normal(source, state);\n  }\n  return {\n    name: \"stex\",\n    startState: function() {\n      var f = mathMode ? function(source, state) {\n        return inMathMode(source, state);\n      } : normal;\n      return {\n        cmdState: [],\n        f\n      };\n    },\n    copyState: function(s) {\n      return {\n        cmdState: s.cmdState.slice(),\n        f: s.f\n      };\n    },\n    token: function(stream, state) {\n      return state.f(stream, state);\n    },\n    blankLine: function(state) {\n      state.f = normal;\n      state.cmdState.length = 0;\n    },\n    languageData: {\n      commentTokens: { line: \"%\" }\n    }\n  };\n}\nconst stex = mkStex(false);\nconst stexMath = mkStex(true);\nexport {\n  stex,\n  stexMath\n};\n"], "names": [], "mappings": "AAAA,SAAS,MAAM,CAAC,QAAQ,EAAE;AAC1B,EAAE,SAAS,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE;AACvC,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACjC,GAAG;AACH,EAAE,SAAS,WAAW,CAAC,KAAK,EAAE;AAC9B,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AACnC,MAAM,OAAO,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACvD,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,GAAG;AACH,EAAE,SAAS,UAAU,CAAC,KAAK,EAAE;AAC7B,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;AACpC,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;AAC1B,KAAK;AACL,GAAG;AACH,EAAE,SAAS,eAAe,CAAC,KAAK,EAAE;AAClC,IAAI,IAAI,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC;AACjC,IAAI,KAAK,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAClD,MAAM,IAAI,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAC5B,MAAM,IAAI,IAAI,CAAC,IAAI,IAAI,SAAS,EAAE;AAClC,QAAQ,SAAS;AACjB,OAAO;AACP,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,OAAO,EAAE,eAAe,EAAE,WAAW;AACzC,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK,EAAE,CAAC;AACR,GAAG;AACH,EAAE,SAAS,gBAAgB,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE;AAC1D,IAAI,OAAO,WAAW;AACtB,MAAM,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;AAC7B,MAAM,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACzB,MAAM,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;AAC5B,MAAM,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AAC3B,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC3B,MAAM,IAAI,CAAC,eAAe,GAAG,WAAW;AACxC,QAAQ,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC;AACvD,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,WAAW,GAAG,WAAW;AACpC,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC;AACzB,QAAQ,OAAO,SAAS,CAAC;AACzB,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,YAAY,GAAG,WAAW;AACrC,OAAO,CAAC;AACR,KAAK,CAAC;AACN,GAAG;AACH,EAAE,IAAI,OAAO,GAAG,EAAE,CAAC;AACnB,EAAE,OAAO,CAAC,cAAc,CAAC,GAAG,gBAAgB,CAAC,cAAc,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC;AAC3F,EAAE,OAAO,CAAC,eAAe,CAAC,GAAG,gBAAgB,CAAC,eAAe,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;AACpF,EAAE,OAAO,CAAC,YAAY,CAAC,GAAG,gBAAgB,CAAC,YAAY,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AAC1E,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AAChE,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AAC5D,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AAChE,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AAC5D,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AAChE,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AAC9D,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,gBAAgB,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AACpE,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,gBAAgB,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AACpE,EAAE,OAAO,CAAC,UAAU,CAAC,GAAG,gBAAgB,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AACtE,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,WAAW;AAClC,IAAI,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC;AAC1B,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,GAAG,WAAW;AAC7E,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,SAAS,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE;AAC9B,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;AAChB,GAAG;AACH,EAAE,SAAS,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE;AACjC,IAAI,IAAI,IAAI,CAAC;AACb,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,uCAAuC,CAAC,EAAE;AAC/D,MAAM,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC9C,MAAM,IAAI,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;AACrF,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;AACxB,MAAM,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC/B,MAAM,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACnC,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC;AACxB,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;AACtC,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;AACtC,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AAC7B,MAAM,QAAQ,CAAC,KAAK,EAAE,SAAS,OAAO,EAAE,MAAM,EAAE;AAChD,QAAQ,OAAO,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AAClD,OAAO,CAAC,CAAC;AACT,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AAC7B,MAAM,QAAQ,CAAC,KAAK,EAAE,SAAS,OAAO,EAAE,MAAM,EAAE;AAChD,QAAQ,OAAO,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AAClD,OAAO,CAAC,CAAC;AACT,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;AAC5B,MAAM,QAAQ,CAAC,KAAK,EAAE,SAAS,OAAO,EAAE,MAAM,EAAE;AAChD,QAAQ,OAAO,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AACjD,OAAO,CAAC,CAAC;AACT,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AAC3B,MAAM,QAAQ,CAAC,KAAK,EAAE,SAAS,OAAO,EAAE,MAAM,EAAE;AAChD,QAAQ,OAAO,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;AAChD,OAAO,CAAC,CAAC;AACT,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;AAC3B,IAAI,IAAI,EAAE,IAAI,GAAG,EAAE;AACnB,MAAM,MAAM,CAAC,SAAS,EAAE,CAAC;AACzB,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK,MAAM,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,EAAE;AACvC,MAAM,IAAI,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;AAChC,MAAM,IAAI,IAAI,EAAE;AAChB,QAAQ,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;AAC9B,QAAQ,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACrC,OAAO,MAAM;AACb,QAAQ,OAAO,OAAO,CAAC;AACvB,OAAO;AACP,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK,MAAM,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,EAAE;AACvC,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;AAChC,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;AACxB,MAAM,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC/B,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC9B,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAChC,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AACjC,MAAM,IAAI,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AACpC,MAAM,IAAI,IAAI,CAAC,IAAI,IAAI,OAAO,EAAE;AAChC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;AACzC,OAAO;AACP,MAAM,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;AACpC,KAAK;AACL,GAAG;AACH,EAAE,SAAS,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE;AACjD,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE,EAAE;AAC3B,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,IAAI,UAAU,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;AAChD,MAAM,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC9B,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;AACvC,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;AACpC,MAAM,OAAO,sBAAsB,CAAC;AACpC,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;AACtC,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;AACpC,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;AACjC,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,0BAA0B,CAAC,EAAE;AAClD,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,0BAA0B,CAAC,EAAE;AAClD,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK;AACL,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;AAC3B,IAAI,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,EAAE;AACpF,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,EAAE,IAAI,GAAG,EAAE;AACnB,MAAM,MAAM,CAAC,SAAS,EAAE,CAAC;AACzB,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG;AACH,EAAE,SAAS,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE;AACtC,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC;AACrC,IAAI,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,EAAE;AAChC,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;AACpC,MAAM,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AAC/B,MAAM,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AACrB,MAAM,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC9B,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC5B,MAAM,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AACrB,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC5B,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;AACtB,IAAI,OAAO,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACjC,GAAG;AACH,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,UAAU,EAAE,WAAW;AAC3B,MAAM,IAAI,CAAC,GAAG,QAAQ,GAAG,SAAS,MAAM,EAAE,KAAK,EAAE;AACjD,QAAQ,OAAO,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACzC,OAAO,GAAG,MAAM,CAAC;AACjB,MAAM,OAAO;AACb,QAAQ,QAAQ,EAAE,EAAE;AACpB,QAAQ,CAAC;AACT,OAAO,CAAC;AACR,KAAK;AACL,IAAI,SAAS,EAAE,SAAS,CAAC,EAAE;AAC3B,MAAM,OAAO;AACb,QAAQ,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE;AACpC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AACd,OAAO,CAAC;AACR,KAAK;AACL,IAAI,KAAK,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE;AACnC,MAAM,OAAO,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACpC,KAAK;AACL,IAAI,SAAS,EAAE,SAAS,KAAK,EAAE;AAC/B,MAAM,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC;AACvB,MAAM,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,YAAY,EAAE;AAClB,MAAM,aAAa,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AAClC,KAAK;AACL,GAAG,CAAC;AACJ,CAAC;AACI,MAAC,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE;AACtB,MAAC,QAAQ,GAAG,MAAM,CAAC,IAAI;;;;"}