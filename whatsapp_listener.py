#!/usr/bin/env python3
"""
WhatsApp Message Listener and Auto-Responder

This script continuously listens for incoming WhatsApp messages and automatically
responds to them using OpenAI's GPT model through the WhatsApp MCP server.

Features:
- Continuous polling for new messages
- Intelligent responses using OpenAI GPT
- Support for multiple contacts
- Error handling and logging
- Configurable polling interval

Prerequisites:
    pip install "praisonaiagents[llm]" mcp
    export OPENAI_API_KEY="your_openai_api_key"

Make sure:
    - Your WhatsApp bridge is running:
        cd whatsapp-mcp/whatsapp-bridge && go run main.go
    - Your MCP server is accessible via stdio
"""

import time
import os
import logging
from datetime import datetime, timedelta
from typing import Set, Dict, Any
from praisonaiagents import Agent, MCP

# Configuration
MCP_CMD = "python /Users/<USER>/Documents/Arpit/Project/whatsapp-mcp-poc/whatsapp-mcp/whatsapp-mcp-server/main.py"
POLL_INTERVAL = 10  # seconds between checks
MAX_MESSAGE_AGE = 300  # only respond to messages newer than 5 minutes

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('whatsapp_listener.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WhatsAppListener:
    def __init__(self):
        """Initialize the WhatsApp listener with OpenAI agent."""
        self.processed_messages: Set[str] = set()
        self.last_check_time = datetime.now()
        
        # Initialize the agent with comprehensive instructions
        self.agent = Agent(
            instructions=self._get_agent_instructions(),
            llm="gpt-4o-mini",
            tools=MCP(MCP_CMD)
        )
        
        logger.info("WhatsApp Listener initialized successfully")
    
    def _get_agent_instructions(self) -> str:
        """Get comprehensive instructions for the WhatsApp agent."""
        return """
        You are an intelligent WhatsApp assistant that helps users with various tasks.
        
        Your responsibilities:
        1. Monitor incoming WhatsApp messages from all contacts
        2. Analyze each message to understand what the user wants
        3. Provide helpful, accurate, and contextual responses
        4. Handle different types of requests (questions, tasks, conversations)
        5. Be friendly, professional, and concise in your responses
        
        Guidelines:
        - Always respond to new messages you haven't seen before
        - Keep responses concise but helpful
        - If you can't help with something, explain why politely
        - For complex requests, break them down into steps
        - Maintain context from previous messages when relevant
        - Use appropriate tools to gather information when needed
        
        Available tools include:
        - list_chats: Get all available chats
        - list_messages: Get messages with filters
        - send_message: Send replies
        - search_contacts: Find specific contacts
        - get_chat: Get chat details
        - And other WhatsApp-related functions
        
        Always use send_message to reply to users after processing their requests.
        """
    
    def check_for_new_messages(self) -> bool:
        """Check for new messages and process them."""
        try:
            logger.info("Checking for new messages...")
            
            # Use the agent to check for new messages and respond
            result = self.agent.start(
                f"""
                Check for any new WhatsApp messages that arrived in the last {POLL_INTERVAL + 5} seconds.
                For each new message:
                1. Read and understand the message content
                2. Determine the appropriate response based on the user's request
                3. Send a helpful reply using the send_message tool
                4. Be conversational and helpful
                
                Focus on messages that are:
                - Recent (within the last few minutes)
                - From real users (not system messages)
                - Haven't been responded to yet
                
                If there are no new messages, just return "No new messages found."
                """
            )
            
            logger.info(f"Message check completed: {result}")
            return True
            
        except Exception as e:
            logger.error(f"Error checking for new messages: {e}")
            return False
    
    def run_continuous_listener(self):
        """Run the continuous message listener."""
        logger.info(f"Starting WhatsApp listener (polling every {POLL_INTERVAL} seconds)")
        logger.info("Press Ctrl+C to stop the listener")
        
        try:
            while True:
                start_time = time.time()
                
                # Check for new messages
                success = self.check_for_new_messages()
                
                if not success:
                    logger.warning("Failed to check messages, will retry next cycle")
                
                # Calculate sleep time to maintain consistent polling interval
                elapsed_time = time.time() - start_time
                sleep_time = max(0, POLL_INTERVAL - elapsed_time)
                
                if sleep_time > 0:
                    logger.debug(f"Sleeping for {sleep_time:.1f} seconds")
                    time.sleep(sleep_time)
                else:
                    logger.warning(f"Message processing took {elapsed_time:.1f}s, longer than poll interval")
                
        except KeyboardInterrupt:
            logger.info("WhatsApp listener stopped by user")
        except Exception as e:
            logger.error(f"Unexpected error in listener loop: {e}")
            raise
    
    def run_single_check(self):
        """Run a single check for new messages (useful for testing)."""
        logger.info("Running single message check...")
        success = self.check_for_new_messages()
        
        if success:
            logger.info("Single check completed successfully")
        else:
            logger.error("Single check failed")
        
        return success
    
    def test_openai_connection(self):
        """Test OpenAI API connection separately."""
        try:
            logger.info("Testing OpenAI API connection...")
            from openai import OpenAI

            client = OpenAI()  # Uses OPENAI_API_KEY from environment
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": "Say 'OpenAI connection test successful'"}],
                max_tokens=20
            )

            result = response.choices[0].message.content
            logger.info(f"OpenAI test result: {result}")
            return True

        except Exception as e:
            logger.error(f"OpenAI connection test failed: {e}")
            return False

    def run_diagnostics(self):
        """Run comprehensive diagnostics."""
        logger.info("=== Running Diagnostics ===")

        # Test OpenAI connection
        if not self.test_openai_connection():
            logger.error("OpenAI connection failed")
            return False

        # Test WhatsApp MCP connection
        if not self.test_connection():
            logger.error("WhatsApp MCP connection failed")
            return False

        logger.info("=== All Diagnostics Passed ===")
        return True

    def test_connection(self):
        """Test the connection to WhatsApp MCP server."""
        try:
            logger.info("Testing WhatsApp MCP server connection...")

            result = self.agent.start(
                "Test the connection by listing the most recent chats. "
                "Just return a brief summary of how many chats were found."
            )

            logger.info(f"Connection test result: {result}")
            return True

        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False

def main():
    """Main function to run the WhatsApp listener."""
    # Hardcode the OpenAI API key
    OPENAI_API_KEY = "********************************************************************************************************************************************************************"

    # Set the API key in environment
    os.environ['OPENAI_API_KEY'] = OPENAI_API_KEY

    # Remove conflicting OpenAI base URL if it exists (common issue)
    if 'OPENAI_API_BASE' in os.environ:
        logger.warning(f"Removing conflicting OPENAI_API_BASE: {os.environ['OPENAI_API_BASE']}")
        del os.environ['OPENAI_API_BASE']

    # Ensure we're using OpenAI's official endpoint
    logger.info("Using OpenAI's official API endpoint with hardcoded key")
    
    # Initialize the listener
    listener = WhatsAppListener()

    # Run comprehensive diagnostics
    if not listener.run_diagnostics():
        logger.error("Diagnostics failed. Please check the errors above.")
        logger.error("Make sure the WhatsApp bridge is running:")
        logger.error("cd whatsapp-mcp/whatsapp-bridge && go run main.go")
        return

    logger.info("All connections successful!")
    
    # Check command line arguments for mode
    import sys
    if len(sys.argv) > 1:
        if sys.argv[1] == '--single':
            # Run single check mode
            listener.run_single_check()
        elif sys.argv[1] == '--test':
            # Run diagnostics only
            logger.info("Running diagnostics only...")
            success = listener.run_diagnostics()
            if success:
                logger.info("🎉 All tests passed! Ready to run the listener.")
            else:
                logger.error("❌ Some tests failed. Please fix the issues above.")
        else:
            logger.info("Usage: python whatsapp_listener.py [--single|--test]")
            logger.info("  --single: Run single message check")
            logger.info("  --test: Run diagnostics only")
            logger.info("  (no args): Run continuous listener")
    else:
        # Run continuous listener mode
        listener.run_continuous_listener()

if __name__ == "__main__":
    main()
